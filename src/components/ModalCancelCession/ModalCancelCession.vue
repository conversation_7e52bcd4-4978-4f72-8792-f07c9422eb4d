<template>
	<farm-modal v-model="value" :offsetTop="48" :offsetBottom="68" size="sm" @onClose="onClose">
		<template v-slot:header>
			<farm-dialog-header title="Cancelar Cessão" @onClose="onClose" />
		</template>
		<template v-slot:content>
			<farm-caption>
				Deseja realmente cancelar a Cessão? Ela ficará com o estado de “cancelada”
				na listagem das cessões, podendo ser consultado a qualquer momento.
			</farm-caption>
			<farm-caption>
				Seus recebíveis voltarão a ficar disponíveis no Banco de Recebíveis.
			</farm-caption>
			<farm-caption>
				Escreva no campo abaixo
				<b>"CANCELAR"</b> para cancelar a cessão.
			</farm-caption>
			<farm-promptusertoconfirm v-model="inputModel" match="CANCELAR" title="" />
		</template>
		<template v-slot:footer>
			<farm-dialog-footer
				confirmLabel="Sim"
				closeLabel="Cancelar"
				:isConfirmDisabled="!inputModel"
				@onConfirm="onConfirm"
				@onClose="onClose"
			/>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';

export default defineComponent({
	name:"modal-cancel-cession",
	props: {
		value: {
			type: Boolean,
			required: true,
		}
	},
	setup(props, { emit }) {

		const inputModel = ref(false);

		function onClose(): void {
			emit('onClose');
		}

		function onConfirm(): void {
			emit('onConfirm');
		}

		return {
			inputModel,
			onConfirm,
			onClose
		};
	},
});
</script>
