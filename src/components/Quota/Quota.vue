<template>
	<div
		:class="{
			'quota-content': true,
			'small': small,
		}"
	>
		<farm-tooltip>
            <farm-caption variation="semiBold" color="white">
				Parcelado
			</farm-caption>
			<farm-caption variation="regular" color="white">
				Recebível com múltiplos vencimentos.
			</farm-caption>
            <template v-slot:activator>
				<p class="text">P</p>
            </template>
        </farm-tooltip>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
export default defineComponent({
	name:"quota",
	props:{
		small:{
			type: Boolean,
			default: false
		}
	}
});
</script>

<style lang="scss" scoped>
@import './Quota';
</style>
