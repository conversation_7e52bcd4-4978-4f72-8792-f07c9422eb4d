<template>
	<div>
		<farm-row justify="space-between">
			<farm-col cols="12" class="mb-4">
				<farm-idcaption icon="magnify" copyText="">
					<template v-slot:title>
						<farm-heading :type="6"> Selecionar Revenda </farm-heading>
					</template>
					<template v-slot:subtitle>
						O histórico de recebíveis ficará disponível para visualização após a seleção
						da Revenda.
					</template>
				</farm-idcaption>
			</farm-col>
		</farm-row>
		<farm-row justify="space-between" v-if="!isError">
			<farm-col cols="12" md="6">
				<farm-form-mainfilter
					label="Buscar Revenda"
					:hasExtraFilters="false"
					@onInputChange="onInputChangeMainFilter"
				/>
			</farm-col>
			<farm-col cols="12" md="2" align="right">
				<farm-select
					class="mt-8"
					id="cession-sort"
					item-text="label"
					item-value="value"
					v-model="sortModel"
					:items="sort"
					@change="onSortSelect"
				/>
			</farm-col>
		</farm-row>
		<receivables-history-list v-if="!isError" :data="receivablesHistory" />

		<farm-row extra-decrease v-if="!isError && receivablesHistory.length > 0">
			<farm-box>
				<farm-datatable-paginator
					:initialLimitPerPage="INITIAL_LIMIT_PER_PAGE"
					:perPageOptions="perPageOptions"
					:page="page"
					:totalPages="pagination && pagination.totalPages"
					@onChangePage="onChangePage"
					@onChangeLimitPerPage="onChangePageLimit"
				/>
			</farm-box>
		</farm-row>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
	</div>
</template>

<script lang="ts">
import { computed, defineComponent, onMounted, ref } from 'vue';

import LoadingInline from '@/components/LoadingInline';
import { usePageable } from '@/composible/usePageable';

import ReceivablesHistoryList from '../../components/ReceivablesHistoryList';
import { useReceivablesHistory } from '../../composables/useReceivablesHistory';
import { sort } from '../../configurations';

export default defineComponent({
	name: 'tab-receivables-history',
	components: {
		ReceivablesHistoryList,
		LoadingInline,
	},
	setup() {
		const {
			getReceivablesHistory,
			isErrorReceivablesHistory,
			isLoadingReceivablesHistory,
			receivablesHistory,
			receivablesHistoryPagination,
		} = useReceivablesHistory();

		const sortModel = ref('name_ASC');
		const perPageOptions = ref([6, 12, 24, 60, 120]);
		const INITIAL_LIMIT_PER_PAGE = 12;

		const defaultParams = {
			pageNumber: 0,
			limit: INITIAL_LIMIT_PER_PAGE,
			orderby: 'name',
			order: 'ASC',
		};

		const {
			page,
			pagination,
			onChangePage,
			onChangePageLimit,
			onInputChangeMainFilter,
			onSortSelect,
			onFiltersApplied,
		} = usePageable(
			{
				calbackFn: params => {
					console.log('params', params);
					getReceivablesHistory({
						...params,
					});
				},
				filters: defaultParams,
				keyInputSearch: 'nameOrId',
				sort: {
					order: 'ASC',
					orderby: 'name',
				},
				charInputSearch: 1,
				lowercaseSort: true,
			},
			receivablesHistoryPagination
		);

		const isLoading = computed(() => isLoadingReceivablesHistory.value);

		const isError = computed(() => isErrorReceivablesHistory.value);

		function onReload(): void {
			load();
		}

		function load(): void {
			getReceivablesHistory(defaultParams);
		}

		onMounted(() => {
			load();
		});

		return {
			isError,
			isLoading,
			receivablesHistory,
			sortModel,
			sort,
			page,
			pagination,
			INITIAL_LIMIT_PER_PAGE,
			perPageOptions,
			onSortSelect,
			onInputChangeMainFilter,
			onChangePage,
			onChangePageLimit,
			onReload,
			onFiltersApplied,
		};
	},
});
</script>
