<template>
	<div>
		<farm-btn icon color="error" :disabled="item.sectionStatus === 2" @click="onDelete(item)">
			<farm-icon color="error" size="md">delete-outline</farm-icon>
		</farm-btn>
		<farm-loader mode="overlay" v-if="isLoading" />
		<modal-cancel-cession
			v-model="isModalCancelCession"
			v-if="isModalCancelCession"
			@onClose="onCloseModalCancelCession"
			@onConfirm="onConfirmModalCancelCession"
		/>
	</div>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue';

import ModalCancelCession from '@/components/ModalCancelCession';
import { useModal } from '@/composible/useModal';

import { useReloadPage } from '../../composables/useReloadPage';

import { useCancelCession } from './composables/useCancelCession';

export default defineComponent({
	name: 'button-cancel',
	components: {
		ModalCancelCession
	},
	props: {
		item: {
			type: Object,
			required: true,
		},
	},
	setup() {
		const {
			isOpenModal: isModalCancelCession,
			onCloseModal: onCloseModalCancelCession,
			onOpenModal: openModalCancelCession
		} = useModal();

		const {
			deleteCession,
			isLoadingDeleteCession
		} = useCancelCession();

		const { updatedReload } = useReloadPage();

		const cessionCurrent = ref(null);

		const isLoading = computed(() => {
			return isLoadingDeleteCession.value;
		});

		function onDelete(item): void {
			cessionCurrent.value = item;
			openModalCancelCession();
		}

		function redirectHome(): void {
			updatedReload(true);
		}

		function onConfirmModalCancelCession(){
			const id = cessionCurrent.value.id;
			deleteCession({id}, redirectHome);
			onCloseModalCancelCession();
		}

		return {
			isLoading,
			isModalCancelCession,
			onCloseModalCancelCession,
			onDelete,
			onConfirmModalCancelCession
		};
	},
});
</script>

