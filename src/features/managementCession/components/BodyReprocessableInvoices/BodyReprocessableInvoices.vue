<template>
	<farm-container>
		<HeaderForm :data="dataHeader" v-if="dataHeader" hide-copy-btn />
		<farm-row extraDecrease class="mb-4">
			<farm-line noSpacing />
		</farm-row>

		<farm-row justify="space-between" align="center">
			<farm-col cols="12" md="6">
				<farm-form-mainfilter
					label="Buscar Recebível"
					:showFilters="isFilterOpen"
					@onInputChange="handleSearchFilters"
					@onClick="onClickMainFilter"
				/>
			</farm-col>
			<farm-col cols="12" md="4" align="right" class="d-flex justify-end">
				<farm-btn outlined @click="reprocessSelected">Reprocessar Selecionados</farm-btn>
				<farm-btn @click="reprocessAll">Reprocessar Todos</farm-btn>
			</farm-col>
		</farm-row>

		<collapse-transition :duration="300">
			<ReprocessableInvoicesFilters
				v-if="isFilterOpen"
				@applyFilters="applyFilters"
				@clearFilters="clearFilters"
			/>
		</collapse-transition>

		<farm-row justify="space-between" class="mt-2 align-center">
			<farm-col cols="12" md="1">
				<farm-chip v-show="cardHeaderList.length" color="primary" variation="lighten" dense>
					10 selecionados</farm-chip
				>
			</farm-col>
			<farm-col cols="12" md="3" align="right">
				<farm-select
					class="mt-8"
					item-text="label"
					item-value="value"
					v-model="selectedSort"
					:items="reprocessableInvoicesSort"
					@change="changeSort"
				/>
			</farm-col>
		</farm-row>

		<div class="mt-4 mb-8" v-if="cardHeaderList.length">
			<ReprocessableInvoiceCardHeader
				v-for="cardInfo in cardHeaderList"
				:product-id="productId"
				:key="cardInfo.id"
				:info="cardInfo"
			/>
		</div>
		<div v-else class="mt-10 mb-10">
			<FilterEmptyState
				subtitle="Não existem recebíveis passíveis de reprocessamento no momento."
			/>
		</div>

		<!-- <farm-row extra-decrease>
			<farm-box>
				<farm-datatable-paginator
					v-if="pagination && pagination.totalPages > 0"
					:page="page"
					:totalPages="pagination && pagination.totalPages"
					@onChangePage="onChangePage"
					@onChangeLimitPerPage="onChangePageLimit"
				/>
			</farm-box>
		</farm-row>

		<farm-loader mode="overlay" v-if="isLoading" />

		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div> -->
		<farm-row extra-decrease v-if="cardHeaderList.length">
			<farm-box>
				<farm-datatable-paginator
					:page="pagination.page"
					:totalPages="pagination.totalPages"
					@onChangePage="changePage"
					@onChangeLimitPerPage="changePageLimit"
				/>
			</farm-box>
		</farm-row>

		<farm-row extraDecrease class="mb-4">
			<farm-line noSpacing />
		</farm-row>

		<farm-row align="center">
			<farm-col align="end">
				<farm-btn outlined @click="redirectToReprocessableInvoicesListing"
					>Cancelar</farm-btn
				>
			</farm-col>
		</farm-row>
	</farm-container>
</template>

<script lang="ts">
import { ref } from 'vue';
import { onMounted } from 'vue';

import FilterEmptyState from '@/components/FilterEmptyState';
import { useRouter } from '@/composible';
import { useFetchProductHeaders } from '@/features/_composables/useFetchProductHeaders';
import {
	ReprocessableBatch,
	useFetchReprocessableBatches,
} from '@/features/_composables/useFetchReprocessableBatches';

import { HeaderForm } from '../HeaderForm';

import ReprocessableInvoiceCardHeader from './components/ReprocessableInvoicesCardHeader.vue';
import ReprocessableInvoicesFilters, {
	ProcessableInvoicesFilters,
} from './components/ReprocessableInvoicesFilters.vue';

type SortOrder = 'ASC' | 'DESC';

export default {
	name: 'BodyReprocessableInvoices',
	components: {
		HeaderForm,
		ReprocessableInvoiceCardHeader,
		ReprocessableInvoicesFilters,
		FilterEmptyState,
	},
	setup() {
		const REPROCESS_INVOICES_ROUTE = `/admin/wallet/gestao?path=reprocessar_recebiveis`;
		const router = useRouter();
		const isFilterOpen = ref(false);
		const { productId } = router.currentRoute.params;
		const cardHeaderList = ref<ReprocessableBatch[]>([]);
		const { data: productHeadersData, fetchProductHeaders } = useFetchProductHeaders();
		const { data: reprocessableBatchesData, fetchReprocessableBatches } =
			useFetchReprocessableBatches();
		const searchTerm = ref('');
		const filters = ref<ProcessableInvoicesFilters>(null);
		const pagination = ref({
			page: 1,
			totalPages: 0,
			totalItems: 0,
			limit: 10,
		});
		const selectedSort = ref<SortOrder>('ASC');
		const reprocessableInvoicesSort = ref([
			{
				label: 'Envio mais recente - menos recente',
				value: 'ASC',
			},
			{
				label: 'Envio menos recente - mais recente',
				value: 'DESC',
			},
		]);
		const dataHeader = ref({
			title: 'AGRO FORTE',
			dataList: [
				{
					subtitle: 'ID',
					value: null,
					icon: 'account-box-outline',
				},
				{
					subtitle: 'Tipo',
					value: null,
					icon: 'tag-outline',
				},
				{
					subtitle: 'Quantidade de recebíveis',
					value: null,
					icon: 'clipboard-text-outline',
				},
			],
		});

		function redirectToReprocessableInvoicesListing() {
			router.push(REPROCESS_INVOICES_ROUTE);
		}

		function normalizeHeaders() {
			dataHeader.value = {
				title: productHeadersData.value.name,
				dataList: [
					{
						subtitle: 'ID',
						value: productHeadersData.value.id,
						icon: 'account-box-outline',
					},
					{
						subtitle: 'Tipo',
						value: productHeadersData.value.type,
						icon: 'tag-outline',
					},
					{
						subtitle: 'Quantidade de recebíveis',
						value: reprocessableBatchesData.value.totalItems,
						icon: 'clipboard-text-outline',
					},
				],
			};
		}

		async function loadHeaders() {
			await fetchProductHeaders(+productId);
		}

		async function loadProcessableBatches() {
			await fetchReprocessableBatches(+productId, {
				page: pagination.value.page - 1,
				limit: pagination.value.limit,
				order: selectedSort.value,
				orderby: 'createdAt',
				search: searchTerm.value,
				createdAtStart: filters.value?.dateRange?.from,
				createdAtEnd: filters.value?.dateRange?.to,
			});
			console.log(reprocessableBatchesData.value);

			pagination.value.totalItems = reprocessableBatchesData.value.totalItems;
			pagination.value.totalPages = reprocessableBatchesData.value.totalPages;

			cardHeaderList.value = reprocessableBatchesData.value.content;
		}

		async function loadData() {
			await Promise.all([loadHeaders(), loadProcessableBatches()]);
			normalizeHeaders();
		}

		function changePage(page: number) {
			pagination.value.page = page;
			loadData();
		}

		function changePageLimit(limit: number) {
			pagination.value.limit = limit;
			pagination.value.page = 1;

			loadData();
		}

		function handleSearchFilters(search: string) {
			pagination.value.page = 1;
			searchTerm.value = search.trim();
			loadData();
		}

		function applyFilters(filtersEvent: ProcessableInvoicesFilters) {
			filters.value = filtersEvent;
			console.log(filters.value);
			loadData();
		}

		function clearFilters() {
			filters.value = null;
			loadData();
		}

		function changeSort(value: any) {
			selectedSort.value = value;
			console.log(selectedSort.value);
			loadData();
		}

		function reprocessSelected() {
			console.log('Reprocessar selecionados');
		}

		function reprocessAll() {
			console.log('Reprocessar todos');
		}

		function onClickMainFilter() {
			isFilterOpen.value = !isFilterOpen.value;
		}

		onMounted(async () => {
			loadData();
		});

		return {
			productId,
			redirectToReprocessableInvoicesListing,
			changePage,
			changePageLimit,
			handleSearchFilters,
			onClickMainFilter,
			applyFilters,
			clearFilters,
			dataHeader,
			isFilterOpen,
			cardHeaderList,
			pagination,
			changeSort,
			selectedSort,
			reprocessableInvoicesSort,
			reprocessSelected,
			reprocessAll,
		};
	},
};
</script>
