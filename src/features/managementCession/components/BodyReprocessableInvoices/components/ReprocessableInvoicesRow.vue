<template>
	<div>
		<farm-row class="d-flex align-center my-4">
			<farm-col cols="12" md="1" class="d-flex justify-center">
				<farm-checkbox v-model="isSelectAllChecked" :value="true" size="md" />
			</farm-col>
			<farm-col cols="12" md="1" class="d-flex align-center">
				<farm-chip :color="status.color" :dense="true" class="w-100">
					{{ status.text }}
				</farm-chip>
			</farm-col>

			<farm-col cols="12" md="3">
				<div>
					<farm-caption variation="regular" color="black" color-variation="30">
						Nfe
					</farm-caption>
					<farm-subtitle ellipsis variation="medium">
						{{ invoice.danfe }}
					</farm-subtitle>
				</div>
			</farm-col>

			<farm-col cols="12" md="1">
				<div>
					<farm-caption variation="regular" color="black" color-variation="30">
						Reprocessamento
					</farm-caption>
					<farm-subtitle ellipsis variation="medium">
						{{ invoice.tries || '-' }}
					</farm-subtitle>
				</div>
			</farm-col>
			<farm-col cols="12" md="2">
				<div>
					<farm-caption variation="regular" color="black" color-variation="30">
						Motivo OT
					</farm-caption>
					<farm-subtitle ellipsis variation="medium">
						{{ invoice.otReason || '-' }}
					</farm-subtitle>
				</div>
			</farm-col>
			<farm-col cols="12" md="3">
				<div>
					<farm-caption variation="regular" color="black" color-variation="30">
						Motivo OT
					</farm-caption>
					<farm-subtitle ellipsis variation="medium">
						{{ invoice.reason || '-' }}
					</farm-subtitle>
				</div>
			</farm-col>
			<farm-col cols="12" md="1" class="d-flex justify-end">
				<farm-context-menu
					@click="handleContextMenu"
					:items="[
						{
							label: 'Reprocessar',
							handler: 'reprocessInvoice',
							icon: { color: 'primary', type: 'rotate-left' },
						},
						{
							label: 'Detalhes',
							handler: 'showDetails',
							icon: { color: 'primary', type: 'open-in-new' },
						},
						{
							label: 'Remover',
							handler: 'removeInvoice',
							icon: { color: 'error', type: 'trash-can-outline' },
						},
					]"
					@reprocessInvoice="reprocessInvoice"
					@showDetails="showDetails"
					@removeInvoice="removeInvoice"
				/>
			</farm-col>
		</farm-row>
		<farm-line noSpacing />
	</div>
</template>

<script lang="ts">
import { computed, ref } from 'vue';

import {} from '@/features/_composables';
import { ReprocessableBatchesInvoice } from '@/features/_composables/useFetchReprocessableBatchesInvoices';

export default {
	name: 'ReprocessableInvoicesRow',
	props: {
		invoice: {
			type: Object as () => ReprocessableBatchesInvoice,
			required: true,
		},
	},
	setup({ invoice }) {
		const isSelectAllChecked = ref(false);
		const statusOptions = {
			0: {
				color: 'info',
				text: 'Validando',
			},
			1: {
				color: 'error',
				text: 'Não validado',
			},
		};

		const status = computed(() => {
			return statusOptions[invoice.status];
		});

		function handleContextMenu(x) {
			console.log('handleContextMenu', x);
		}

		function reprocessInvoice() {
			console.log('reprocessInvoice');
		}

		function showDetails() {
			console.log('showDetails');
		}

		function removeInvoice() {
			console.log('removeInvoice');
		}

		return {
			isSelectAllChecked,
			status,
			handleContextMenu,
			reprocessInvoice,
			showDetails,
			removeInvoice,
		};
	},
};
</script>
