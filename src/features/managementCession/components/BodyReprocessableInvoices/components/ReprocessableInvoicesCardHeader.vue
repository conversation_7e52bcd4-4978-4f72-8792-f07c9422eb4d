<template>
	<farm-card class="mb-4">
		<farm-card-content gutter="md">
			<farm-row>
				<farm-col cols="12" md="1" class="d-flex justify-center align-center">
					<div>
						<farm-checkbox v-model="isSelectAllChecked" :value="true" size="md" />
						<farm-caption
							class="count mt-1"
							variation="regular"
							color="black"
							color-variation="30"
						>
							{{ selectedList.length }}/{{ propsInfo.invoices }}
						</farm-caption>
					</div>
				</farm-col>
				<farm-col cols="12" md="1">
					<farm-caption variation="regular" color="black" color-variation="30">
						ID
					</farm-caption>
					<farm-subtitle ellipsis variation="medium">{{ propsInfo.id }}</farm-subtitle>
				</farm-col>
				<farm-col cols="12" md="2">
					<farm-caption variation="regular" color="black" color-variation="30">
						Nome do Arquivo
					</farm-caption>
					<farm-subtitle ellipsis variation="medium">{{
						propsInfo.filename
					}}</farm-subtitle>
				</farm-col>
				<farm-col cols="12" md="3">
					<farm-caption variation="regular" color="black" color-variation="30">
						Data de Envio
					</farm-caption>
					<farm-subtitle ellipsis variation="medium">
						{{ formatDate(propsInfo.createdAt) }}
					</farm-subtitle>
				</farm-col>
				<farm-col cols="12" md="3">
					<farm-caption variation="regular" color="black" color-variation="30">
						Recebíveis para Reprocessamento
					</farm-caption>
					<farm-subtitle ellipsis variation="medium">
						{{ propsInfo.invoices }}
					</farm-subtitle>
				</farm-col>
				<farm-col cols="12" md="1" class="d-flex justify-end align-center">
					<farm-chip :color="status[propsInfo.status].color" :dense="true" class="w-100">
						{{ status[propsInfo.status].text }}
					</farm-chip>
				</farm-col>
				<farm-col cols="12" md="1" class="d-flex justify-end align-center">
					<farm-btn icon @click="isOpen = !isOpen">
						<farm-icon size="md">chevron-down</farm-icon>
					</farm-btn>
				</farm-col>
			</farm-row>
		</farm-card-content>
		<collapse-transition :duration="300" v-show="isOpen">
			<farm-card-content gutter="xs" background="base">
				<ReprocessableInvoicesRow
					v-for="invoice in reprocessableInvoices"
					:invoice="invoice"
					:key="uniqueId(invoice.danfe)"
				/>
			</farm-card-content>
		</collapse-transition>
	</farm-card>
</template>

<script lang="ts">
import { ref, watch } from 'vue';

import { ReprocessableBatch } from '@/features/_composables/useFetchReprocessableBatches';
import {
	ReprocessableBatchesInvoice,
	useFetchReprocessableBatchesInvoices,
} from '@/features/_composables/useFetchReprocessableBatchesInvoices';

import ReprocessableInvoicesRow from './ReprocessableInvoicesRow.vue';

export default {
	name: 'ReprocessableInvoiceCardHeader',
	components: {
		ReprocessableInvoicesRow,
	},
	props: {
		info: {
			type: Object as () => ReprocessableBatch,
			required: true,
		},
		productId: {
			type: String as () => string,
			required: true,
		},
	},
	setup({ info: propsInfo, productId }) {
		const isOpen = ref(false);
		const isSelectAllChecked = ref(false);
		const selectedList = ref<number[]>([]);
		const formatDate = (date: string) =>
			new Date(date).toLocaleString('pt-BR', { dateStyle: 'short' });
		const status = {
			1: {
				color: 'warning',
				text: 'Em andamento',
			},
			2: {
				color: 'info',
				text: 'Validando',
			},
			3: {
				color: 'error',
				text: 'Erro',
			},
			4: {
				color: 'success',
				text: 'Concluído',
			},
		};
		const { data, fetchReprocessableBatchesInvoices } = useFetchReprocessableBatchesInvoices();
		const reprocessableInvoices = ref<ReprocessableBatchesInvoice[]>([]);
		const uniqueId = (id: string) =>
			`${id}-${Date.now().toString(36) + Math.random().toString(36).slice(2, 7)}`;

		async function loadReprocessableInvoices() {
			await fetchReprocessableBatchesInvoices({
				productId,
				batchId: propsInfo.id,
				filters: { limit: 10, page: 0, order: 'ASC' },
			});

			reprocessableInvoices.value = data.value.content;
		}

		watch(isOpen, async isOpenState => {
			if (isOpenState) {
				loadReprocessableInvoices();
			}
		});

		return {
			propsInfo,
			isOpen,
			isSelectAllChecked,
			selectedList,
			status,
			reprocessableInvoices,
			uniqueId,
			formatDate,
		};
	},
};
</script>
