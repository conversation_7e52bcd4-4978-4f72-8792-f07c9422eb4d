<template>
	<farm-box>
		<farm-form class="mb-6">
			<farm-row>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-status"> Status </farm-label>
					<farm-select
						v-model="selectedStatus"
						:items="statusList"
						item-text="label"
						item-value="value"
					/>
				</farm-col>

				<farm-col cols="12" md="3">
					<farm-label for="form-filter-due-date"> Data de Envio (Início/fim) </farm-label>
					<farm-input-rangedatepicker
						v-model="dateRange"
						inputId="form-filter-date-range"
						@input="inputDatepickerDateRange"
					/>
				</farm-col>
			</farm-row>
			<farm-row>
				<farm-col>
					<farm-btn-confirm
						outlined
						title="Aplicar Filtros"
						@click="onFilterConfirm"
						class="ml-0"
					>
						Aplicar Filtros
					</farm-btn-confirm>
					<farm-btn plain depressed title="Limpar Filtros" @click="onFilterClear">
						Limpar Filtros
					</farm-btn>
				</farm-col>
			</farm-row>
		</farm-form>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';

export type ProcessableInvoiceFilterDate = `${number}-${number}-${number}`;

export type ProcessableInvoicesFilters = {
	status: number | null;
	dateRange: {
		from: ProcessableInvoiceFilterDate;
		to: ProcessableInvoiceFilterDate;
	} | null;
};

export default defineComponent({
	name: 'processable-invoices-filters',
	props: {
		statusValues: {
			type: Array,
			default: () => [],
		},
	},
	setup(_, { emit }) {
		const selectedStatus = ref<number>(null);
		const dateRange = ref([]);
		const emissionDate = ref([]);
		const filters = ref<ProcessableInvoicesFilters>({
			status: null,
			dateRange: null,
		});
		const statusList = [
			{
				value: 1,
				label: 'Em andamento',
			},
			{
				value: 2,
				label: 'Validando',
			},
			{
				value: 3,
				label: 'Erro',
			},
			{
				value: 4,
				label: 'Concluído',
			},
		];

		function onFilterClear(): void {
			selectedStatus.value = null;
			dateRange.value = null;

			emit('clearFilters');
		}

		function onFilterConfirm(): void {
			const transformDate = (rawDate: string) => {
				const date = new Date(rawDate);
				const formatedDate = date
					.toLocaleDateString('pt-BR', {
						year: '2-digit',
						month: '2-digit',
						day: '2-digit',
					})
					.replace(/\//g, '-');

				return formatedDate as ProcessableInvoiceFilterDate;
			};
			const [from, to] = dateRange.value;
			filters.value = {
				status: selectedStatus.value,
				dateRange: { from: transformDate(from), to: transformDate(to) },
			};

			emit('applyFilters', filters);
		}

		function inputDatepickerDateRange(range: ProcessableInvoiceFilterDate[]): void {
			const [from, to] = range;

			dateRange.value = range;
			filters.value.dateRange = {
				from,
				to,
			};
		}

		return {
			dateRange,
			emissionDate,
			statusList,
			selectedStatus,
			onFilterConfirm,
			onFilterClear,
			inputDatepickerDateRange,
		};
	},
});
</script>
