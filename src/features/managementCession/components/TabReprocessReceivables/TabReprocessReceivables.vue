<template>
	<div>
		<farm-row justify="space-between">
			<farm-col cols="12" class="mb-4">
				<farm-idcaption icon="cached" copyText="">
					<template v-slot:title>
						<farm-heading :type="6"> Reprocessar Recebíveis </farm-heading>
					</template>
					<template v-slot:subtitle>
						Selecione o Parceiro para visualizar os recebíveis aptos ao reprocessamento.
					</template>
				</farm-idcaption>
			</farm-col>
		</farm-row>

		<farm-row justify="space-between mt-2 ">
			<farm-col cols="12" md="6">
				<farm-form-mainfilter
					label="Buscar Parceiro"
					:hasExtraFilters="false"
					tooltip="Realize sua busca pelo Nome ou ID do Parceiro."
					@onInputChange="inputChangeMainFilter"
				/>
			</farm-col>
			<farm-col cols="12" md="2" align="right">
				<farm-select
					class="mt-8"
					item-text="label"
					item-value="value"
					v-model="selectedSort"
					:items="sortOptions"
					@change="changeSort"
				/>
			</farm-col>
		</farm-row>

		<farm-row>
			<farm-col
				cols="12"
				md="4"
				v-for="item in receivableHistoryList"
				:key="item.id"
				class="mb-4"
			>
				<ReprocessReceivablesCard :card-data="item" />
			</farm-col>
		</farm-row>

		<farm-row extra-decrease v-if="!receivableHistoryError && receivableHistoryList.length > 0">
			<farm-box>
				<farm-datatable-paginator
					:initialLimitPerPage="itemsPerPage"
					:page="currentPage"
					:perPageOptions="perPageOptions"
					:totalPages="totalPages"
					@onChangePage="changePage"
					@onChangeLimitPerPage="changePageLimit"
				/>
			</farm-box>
		</farm-row>
		<farm-row
			justify="center"
			v-if="receivableHistoryList.length === 0 && !isReceivableHistoryLoading"
		>
			<FilterEmptyState :subtitle="emptyStateMessage" class="my-10" />
		</farm-row>

		<farm-loader mode="overlay" v-if="isReceivableHistoryLoading" />
	</div>
</template>

<script lang="ts">
import { computed, defineComponent, onMounted, ref } from 'vue';

import FilterEmptyState from '@/components/FilterEmptyState';
import { useFetchReprocessableInvoices } from '@/features/_composables';

import { ReprocessableInvoicesParams } from '../../services';

import ReprocessReceivablesCard from './components/ReprocessReceivablesCard.vue';

type SortOrder = ReprocessableInvoicesParams['order'];

export default defineComponent({
	name: 'tab-reprocess-receivables',
	components: {
		ReprocessReceivablesCard,
		FilterEmptyState,
	},
	setup() {
		const {
			data: receivablesHistoryData,
			error: receivableHistoryError,
			state: receivableHistoryState,
			fetchReprocessableInvoices,
		} = useFetchReprocessableInvoices();
		const receivablesHistorySort = [
			{
				label: 'Alfabética A-Z',
				value: 'ASC',
			},
			{
				label: 'Alfabética Z-A',
				value: 'DESC',
			},
		];

		const selectedSort = ref<SortOrder>('ASC');
		const sortOptions = ref(receivablesHistorySort);
		const itemsPerPage = ref(12);
		const currentPage = ref(1);
		const totalPages = ref(1);
		const perPageOptions = ref([6, 12, 24, 60, 120]);
		const emptyStateMessage = ref('Nenhum recebível encontrado');

		const filters = ref<ReprocessableInvoicesParams>({
			limit: 12,
			search: '',
			order: 'ASC',
			orderby: 'name',
			page: 0,
		});
		const receivableHistoryList = ref([]);

		const isReceivableHistoryLoading = computed(
			() => receivableHistoryState.value === 'LOADING'
		);

		computed(() => {
			filters.value.order = selectedSort.value;
		});

		function changeSort(value: SortOrder): void {
			selectedSort.value = value;
			filters.value.order = value;
			loadReprocessableInvoices();
		}

		function inputChangeMainFilter(value: string): void {
			filters.value.page = 0;
			filters.value.search = value;
			currentPage.value = 1;
			loadReprocessableInvoices();
		}

		function changePage(page: number): void {
			filters.value.page = page - 1;
			currentPage.value = page;
			loadReprocessableInvoices();
		}

		function changePageLimit(limit: number): void {
			filters.value.page = 0;
			filters.value.limit = limit;
			currentPage.value = 1;
			loadReprocessableInvoices();
		}

		async function loadReprocessableInvoices() {
			await fetchReprocessableInvoices({ filters: filters.value });

			if (receivableHistoryError.value) {
				receivableHistoryList.value = [];
				totalPages.value = 0;
				emptyStateMessage.value = 'Tente filtrar novamente sua pesquisa.';
				return;
			}

			receivableHistoryList.value = receivablesHistoryData.value.content;
			totalPages.value = receivablesHistoryData.value.totalPages;
		}

		async function reloadReceivableHistory() {
			loadReprocessableInvoices();
		}

		onMounted(async () => {
			loadReprocessableInvoices();
		});

		return {
			receivablesHistorySort,
			sortOptions,
			selectedSort,
			receivableHistoryList,
			receivableHistoryError,
			isReceivableHistoryLoading,
			currentPage,
			perPageOptions,
			totalPages,
			itemsPerPage,
			emptyStateMessage,
			changePage,
			changePageLimit,
			reloadReceivableHistory,
			inputChangeMainFilter,
			changeSort,
		};
	},
});
</script>
