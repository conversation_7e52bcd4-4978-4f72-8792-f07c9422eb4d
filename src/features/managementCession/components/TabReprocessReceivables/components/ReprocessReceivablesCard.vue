<template>
	<Cards>
		<template slot="header">
			<farm-row align="center">
				<farm-col cols="8">
					<CardTitleHeader :value="cardData.name" class="mb-1" />
				</farm-col>
				<farm-col cols="4" align="end">
					<farm-btn icon @click="() => redirectToHistory(cardData.id)">
						<farm-icon size="lg">chevron-right</farm-icon>
					</farm-btn>
				</farm-col>
			</farm-row>
		</template>
		<template slot="body">
			<farm-row>
				<farm-col cols="3">
					<CardTextBody label="ID" :value="cardData.id" />
				</farm-col>
				<farm-col cols="3">
					<CardTextBody label="Tipo" :value="cardData.type" />
				</farm-col>
				<farm-col cols="6">
					<CardTextBody label="Quantidade de Recebíveis" :value="cardData.invoices" />
				</farm-col>
			</farm-row>
		</template>
	</Cards>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import CardListTextHeader from '@/components/CardListTextHeader';
import CardTextBody from '@/components/CardTextBody';
import CardTitleHeader from '@/components/CardTitleHeader';
import Cards from '@/components/Cards';
// import { useRouter } from '@/composible';
import { ReprocessableInvoice } from '@/features/_composables';

export default defineComponent({
	name: 'reprocessable-invoice-card',
	components: {
		Cards,
		CardTextBody,
		CardTitleHeader,
		CardListTextHeader,
	},
	props: {
		cardData: {
			type: Object as () => ReprocessableInvoice,
			default: () => ({}),
		},
	},
	setup({ cardData }) {
		// const router = useRouter();
		function redirectToHistory(productId: number) {
			console.log('ReprocessReceivablesCard::productId', productId);
			// router.push(`/admin/wallet/gestao/${productId}/reprocessable-invoices`);
		}

		return { redirectToHistory, cardData };
	},
});
</script>
