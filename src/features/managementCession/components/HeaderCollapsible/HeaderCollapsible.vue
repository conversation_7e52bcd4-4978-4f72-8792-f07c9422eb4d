<template>
	<farm-row>
		<farm-col cols="12">
			<div class="header-collapsible">
				<div class="header-collapsible-items">
					<farm-caption
						class="mr-2"
						variation="regular">
						Revenda:
					</farm-caption>
					<farm-subtitle
						variation="medium"
						:type="1">
						{{ resale }}
					</farm-subtitle>
				</div>
				<div class="header-collapsible-line"></div>
				<div class="header-collapsible-items">
					<farm-caption
						class="mr-2"
						variation="regular">
						Cessões:
					</farm-caption>
					<farm-subtitle
						variation="medium"
						:type="1">
						{{ cession }}
					</farm-subtitle>
				</div>
			</div>
		</farm-col>
	</farm-row>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
	name:'header-collapsible',
	props: {
		resale: {
			type: String,
			required: true,
		},
		cession: {
			type: Number,
			required: true,
		},
	},
	setup() {

	},
});

</script>
<style lang="scss" scoped>
@import './HeaderCollapsible';
</style>
