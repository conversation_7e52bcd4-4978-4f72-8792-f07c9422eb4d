<template>
	<farm-box>
		<farm-form class="mb-6">
			<farm-row>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-status"> Status </farm-label>
					<farm-select
						id="form-filter-status"
						v-model="statusModel"
						:items="statusListSelect"
						item-text="label"
						item-value="id"
					/>
				</farm-col>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-createdAt"> Criação (Início/fim)  </farm-label>
					<farm-input-rangedatepicker
						ref="datepickerCreatedAt"
						inputId="form-filter-createdAt"
						v-model="createdAtModel"
						@input="onInputDatepickerCreatedAt"
					/>
				</farm-col>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-updatedAt">
						Atualização (Início/fim)
					</farm-label>
					<farm-input-rangedatepicker
						ref="datepickerUpdatedAtt"
						inputId="form-filter-updatedAt"
						v-model="updatedAtModel"
						@input="onInputDatepickerUpdatedAt"
					/>
				</farm-col>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-dateDisbursement">
						Desembolso (Início/fim)
					</farm-label>
					<farm-input-rangedatepicker
						ref="datepickerDateDisbursement"
						inputId="form-filter-expiredAtRange"
						v-model="dateDisbursementModel"
						@input="onInputDatepickerDateDisbursement"
					/>
				</farm-col>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-revenda"> Revenda </farm-label>
					<farm-select
						id="form-filter-revenda"
						v-model="resaleModel"
						:items="resale"
						item-text="label"
						item-value="id"
					/>
				</farm-col>
			</farm-row>
			<farm-row>
				<farm-col>
					<farm-btn-confirm
						outlined
						class="farm-btn--responsive"
						title="Aplicar Filtros"
						@click="onFilterConfirm"
					>
						Aplicar Filtros
					</farm-btn-confirm>
					<farm-btn
						plain
						depressed
						class="farm-btn--responsive ml-0 ml-sm-2 mt-2 mt-sm-0"
						title="Limpar Filtros"
						@click="onFilterClear"
					>
						Limpar Filtros
					</farm-btn>
				</farm-col>
			</farm-row>
		</farm-form>
		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed, watch } from 'vue';

import { useReloadPage } from '../../composables/useReloadPage';
import { status } from '../../configurations';

import { useResale } from './composables/useResale';


export default defineComponent({
	name:"filters",
	setup(_, { emit }) {

		const {
			getResale,
			isLoadingResale,
			resale
		} = useResale();

		const {
			reset,
			statusReload
		} = useReloadPage();

		const statusModel = ref('');
		const resaleModel = ref('');
		const createdAtModel = ref([]);
		const updatedAtModel = ref([]);
		const dateDisbursementModel = ref([]);
		const statusListSelect = ref(status);

		const isLoading = computed(() => {
			return isLoadingResale.value;
		});

		function checkValueDatepicker(value: string) {
			if (value.length === 2) {
				const startDate = new Date(value[0]);
				const endDate = new Date(value[1]);
				if (startDate > endDate) {
					return [value[1], value[0]];
				}
			}
			return value;
		}

		function checkDatepickerCompleted(data, keyStart, keyEnd) {
			return {
				[keyStart]: data[0],
				[keyEnd]: data[1]
			};
		}

		function validDataFilter() {
			let obj = {};
			if(statusModel.value) {
				obj = {
					status: statusModel.value
				};
			}

			if(resaleModel.value) {
				obj = {
					...obj,
					productId: resaleModel.value
				};
			}
			if(createdAtModel.value && createdAtModel.value.length === 2){
				const result = checkDatepickerCompleted(
					createdAtModel.value,
					'createdAtStart',
					'createdAtEnd'
				);
				obj = {
					...obj,
					...result
				};
			}
			if(updatedAtModel.value && updatedAtModel.value.length === 2){
				const result = checkDatepickerCompleted(
					updatedAtModel.value,
					'updatedAtStart',
					'updatedAtEnd'
				);
				obj = {
					...obj,
					...result
				};
			}
			if(dateDisbursementModel.value && dateDisbursementModel.value.length === 2){
				const result = checkDatepickerCompleted(
					dateDisbursementModel.value,
					'disbursementStart',
					'disbursementEnd'
				);
				obj = {
					...obj,
					...result
				};
			}
			return obj;
		}

		function onInputDatepickerCreatedAt(value: string): void {
			createdAtModel.value = checkValueDatepicker(value);
		}

		function onInputDatepickerUpdatedAt(value: string): void {
			updatedAtModel.value = checkValueDatepicker(value);
		}

		function onInputDatepickerDateDisbursement(value: string): void {
			dateDisbursementModel.value = checkValueDatepicker(value);
		}

		function onFilterConfirm(): void {
			emit('onApply', validDataFilter());
			emit('onFiltersApplied', true);
		}

		function onFilterClear(): void {
			statusModel.value = null;
			resaleModel.value = null;
			createdAtModel.value = null;
			updatedAtModel.value = null;
			dateDisbursementModel.value = null;
			emit('onApply', {});
			emit('onFiltersApplied', false);
		}

		function load(): void {
			getResale();
		}

		onMounted(() => {
			load();
		});

		watch(statusReload, (newValue: boolean): void => {
			if (newValue) {
				load();
				reset();
			}
		});

		return {
			isLoading,
			statusModel,
			resale,
			resaleModel,
			createdAtModel,
			updatedAtModel,
			dateDisbursementModel,
			statusListSelect,
			onFilterConfirm,
			onFilterClear,
			onInputDatepickerCreatedAt,
			onInputDatepickerUpdatedAt,
			onInputDatepickerDateDisbursement
		};
	},
});
</script>
