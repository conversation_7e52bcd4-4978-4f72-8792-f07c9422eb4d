

function mapToDataCessions(data){
	return data.map((item) => {
		return {
			id: item.id,
			receivables: item.invoices,
			approved: item.approved,
			refused:item.notApproved,
			createdAt: item.createdAt,
			updatedAt: item.updatedAt,
			dateDisbursement: item.disbursement,
			valueNominal: item.nominalValue,
			totalFreeValue: item.freeValue,
			totalLiquidValue: item.netValue,
			sectionStatus: item.status,
		};
	});
}
export function builderCession(response) {
	const content = response.content.map((item)=>{
		return {
			id: item.id,
			name: item.name,
			cessions: mapToDataCessions(item.cessions)
		};
	});
	return {
		content,
		total: response.totalPages,
		pagination:{
			pageNumber: response.page,
			pageSize: response.size,
			sort: null,
			totalElements: response.totalItems,
			totalPages: response.totalPages,
		}
	};
}
