export const logItemMapper = {
	'1': {
		icon: 'cancel',
		status: 'error',
	},
	'2': {
		icon: 'currency-usd',
		status: 'success',
	},
	'3': {
		icon: 'check',
		status: 'warning',
	},
	'4': {
		icon: 'delete-outline',
		status: 'error',
	},
	'5': {
		icon: 'check',
		status: 'warning',
	},
	'6': {
		icon: 'close',
		status: 'error',
	},
	'7': {
		icon: 'check',
		status: 'success',
	},
	'8': {
		icon: 'check',
		status: 'success',
	},
	'9': {
		icon: 'check',
		status: 'warning',
	},
	'10': {
		icon: 'cancel',
		status: 'error',
	},
	'11': {
		icon: 'clock-outline',
		status: 'info',
	},
	'12': {
		icon: 'cancel',
		status: 'error',
	},
	'13': {
		icon: 'delete-outline',
		status: 'error',
	},
	'14': {
		icon: 'check',
		status: 'success',
	},
	'15': {
		icon: 'clock-outline',
		status: 'info',
	},
	'16': {
		icon: 'pencil',
		status: 'info',
	},
	'17': {
		icon: 'calendar-range',
		status: 'error',
	},
	'18': {
		icon: 'close',
		status: 'error',
	},
	'19': {
		icon: 'check',
		status: 'success',
	},
	'20': {
		icon: 'cancel',
		status: 'error',
	},
	'21': {
		icon: 'clock-outline',
		status: 'info',
	},
	'22': {
		icon: 'file-outline',
		status: 'success',
	},
	'45': {
		icon: 'cancel',
		status: 'error',
	},
	'46': {
		icon: 'clipboard-check-outline',
		status: 'info',
	},
	'47': {
		icon: 'clipboard-check-outline',
		status: 'info',
	},
};
