import { environment } from '@farm-investimentos/front-mfe-libs-ts';

import { client } from '@/configurations/services/superCession';

const domainJava = environment.apiSuperCessaoV2;

const receivablesDomain = environment.apiSuperCessao;

const domainNodeRegister = environment.apiCadastrosUrlV3;

export const getCession = ({ filters }) => {
	const url = `/api/v1/operation?${filters}`;
	return client.get(`${url}`);
};

export const deleteCession = ({ id }) => {
	const url = `/api/v1/operation/${id}`;
	return client.delete(`${url}`);
};

export const getResale = () => {
	const url = `/api/v1/operation/products-with-operations`;
	return client.get(`${url}`);
};

export const getDateAndHour = () => {
	const url = `/parameters/configuration/v1/deadline`;
	return client.get(`${domainJava}${url}`);
};

export const editDateAndHour = ({ payload }) => {
	const url = `/parameters/configuration/v1/deadline`;
	return client.patch(`${domainJava}${url}`, payload);
};

export const getReceivablesHistory = ({ filters }: { filters: string }) => {
	const url = `/receivables/orders/v1/products?${filters}`;
	return client.get(`${domainJava}${url}`);
};

export type ReprocessableInvoicesParams = {
	page: number;
	limit: number;
	order: 'ASC' | 'DESC';
	search?: string;
	orderby?: 'name';
};

export type ReprocessableBatchesInvoicesArgs = {
	productId: string;
	batchId: number;
	filters?: ReprocessableInvoicesParams;
};

export const getReprocessableInvoices = ({ filters }: { filters: ReprocessableInvoicesParams }) => {
	const url = `/api/v1/product/reprocessable-invoices`;

	return client.get(`${receivablesDomain}${url}`, {
		params: filters,
	});
};

export const getReprocessableBatchesInvoices = ({
	productId,
	batchId,
	filters,
}: ReprocessableBatchesInvoicesArgs) => {
	const url = `/api/v1/product/${productId}/reprocessable-batches/${batchId}/reprocessable-invoices`;

	return client.get(`${receivablesDomain}${url}`, {
		params: filters,
	});
};

export type ReprocessableBatchesParams = {
	page: number;
	limit: number;
	order: 'ASC' | 'DESC';
	search?: string;
	orderby: 'createdAt';
	createdAtStart?: `${number}-${number}-${number}`;
	createdAtEnd?: `${number}-${number}-${number}`;
};

export const getReprocessableBatches = ({
	productId,
	filters,
}: {
	productId: number;
	filters: ReprocessableBatchesParams;
}) => {
	const url = `/api/v1/product/${productId}/reprocessable-batches`;

	return client.get(`${receivablesDomain}${url}`, {
		params: filters,
	});
};

export const getProductHeaders = (productId: number) => {
	const url = `/api/v1/product/${productId}/headers`;

	return client.get(`${domainNodeRegister}${url}`);
};

export const getReceivablesHistoryLog = (productId: number) => {
	const url = `/receivables/history/v1/${productId}`;
	return client.get(`${domainJava}${url}`);
};

export const getReceivablesHistoryCardHeader = ({ productId, params, shouldFilter }) => {
	const urlParams = new URLSearchParams(params);
	let url = `${domainJava}/receivables/orders/v1/products/${productId}?${urlParams}`;

	if (shouldFilter) {
		url = `${domainJava}/receivables/history/v1/products/${productId}/filter?${urlParams}`;
	}

	return client.get(url);
};

export const getReceivablesHistoryCardDetails = ({ productId, providerDocument, params }) => {
	const urlParams = new URLSearchParams(params);
	const url = `${domainJava}/receivables/orders/v1/products/${productId}/${providerDocument}?order=ASC&orderby=id&${urlParams}`;
	return client.get(url);
};

export const getFinancialVehicleMinimumValue = ({ filters }) => {
	const url = `/api/v1/financial-vehicle/minimum-value?${filters}`;
	return client.get(`${domainNodeRegister}${url}`);
};

export const updatedFinancialVehicleMinimumValue = ({ payload }) => {
	const url = `/api/v1/financial-vehicle/minimum-value`;
	return client.patch(`${domainNodeRegister}${url}`, payload.payload);
};

export const updatedAllFinancialVehicleMinimumValue = ({ payload }) => {
	const url = `/api/v1/financial-vehicle/minimum-value`;
	return client.post(`${domainNodeRegister}${url}`, payload);
};
