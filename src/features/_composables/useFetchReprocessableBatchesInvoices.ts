import { ref, Ref } from 'vue';

import {
	getReprocessableBatchesInvoices,
	ReprocessableBatchesInvoicesArgs,
} from '../managementCession/services/services';

type ServiceState = 'IDLE' | 'LOADING' | 'SUCCESS' | 'ERROR';

export type ReprocessableBatchesInvoice = {
	reason: string;
	otReason: string | null;
	tries: number | null;
	status: number;
	danfe: string;
	providerName: string;
	providerDocument: string;
	draweeName: string;
	draweeDocument: string;
	emissionDate: string;
	expirationDate: string;
	number: string;
	part: number;
	totalParts: number;
	cfopCode: string;
};

type ReprocessableBatchesInvoicesResponse = {
	content: ReprocessableBatchesInvoice[];
	page: number | null;
	size: number | null;
	totalPages: number | null;
	totalItems: number;
};

export function useFetchReprocessableBatchesInvoices() {
	const state: Ref<ServiceState> = ref('IDLE');
	const data = ref<ReprocessableBatchesInvoicesResponse>(null);
	const error = ref<string>(null);

	const fetchReprocessableBatchesInvoices = async ({
		productId,
		batchId,
		filters,
	}: ReprocessableBatchesInvoicesArgs) => {
		state.value = 'LOADING';
		error.value = null;

		try {
			const response = await getReprocessableBatchesInvoices({ productId, batchId, filters });

			data.value = response.data;
			state.value = 'SUCCESS';
		} catch (err: any) {
			error.value = err?.response?.data?.message || 'An unexpected error occurred.';
			state.value = 'ERROR';
		}
	};

	return {
		state,
		data,
		error,
		fetchReprocessableBatchesInvoices,
	};
}
