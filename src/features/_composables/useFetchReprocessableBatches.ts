import { ref, Ref } from 'vue';

import { getReprocessableBatches, ReprocessableBatchesParams } from '../managementCession/services';

type ServiceState = 'IDLE' | 'LOADING' | 'SUCCESS' | 'ERROR';

export type ReprocessableBatch = {
	id: number;
	filename: string;
	createdAt: string;
	invoices: string;
	status: number;
};

export type ReprocessableBatchesResponse = {
	content: ReprocessableBatch[];
	page: number;
	size: number;
	totalPages: number;
	totalItems: number;
};

export function useFetchReprocessableBatches() {
	const state: Ref<ServiceState> = ref('IDLE');
	const data = ref<ReprocessableBatchesResponse | null>(null);
	const error = ref<string | null>(null);

	const fetchReprocessableBatches = async (
		productId: number,
		query: ReprocessableBatchesParams
	) => {
		state.value = 'LOADING';
		error.value = null;

		try {
			const response = await getReprocessableBatches({ productId, filters: query });
			data.value = response.data;
			state.value = 'SUCCESS';
		} catch (err: any) {
			error.value = err?.response?.data?.message || 'An unexpected error occurred.';
			state.value = 'ERROR';
		}
	};

	return {
		state,
		data,
		error,
		fetchReprocessableBatches,
	};
}
