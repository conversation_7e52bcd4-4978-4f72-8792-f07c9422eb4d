import { ref, Ref } from 'vue';

import {
	getReprocessableInvoices,
	ReprocessableInvoicesParams,
} from '../managementCession/services';

type ServiceState = 'IDLE' | 'LOADING' | 'SUCCESS' | 'ERROR';

export type ReprocessableInvoice = {
	id: number;
	name: string;
	type: string;
	invoices: string;
};

type ReprocessableInvoicesResponse = {
	content: ReprocessableInvoice[];
	page: number | null;
	size: number | null;
	totalPages: number | null;
	totalItems: number;
};

export function useFetchReprocessableInvoices() {
	const state: Ref<ServiceState> = ref('IDLE');
	const data = ref<ReprocessableInvoicesResponse>(null);
	const error = ref<string>(null);

	const fetchReprocessableInvoices = async ({
		filters,
	}: {
		filters: ReprocessableInvoicesParams;
	}) => {
		state.value = 'LOADING';
		error.value = null;

		try {
			const response = await getReprocessableInvoices({ filters });

			data.value = response.data;
			state.value = 'SUCCESS';
		} catch (err: any) {
			error.value = err?.response?.data?.message || 'An unexpected error occurred.';
			state.value = 'ERROR';
		}
	};

	return {
		state,
		data,
		error,
		fetchReprocessableInvoices,
	};
}
