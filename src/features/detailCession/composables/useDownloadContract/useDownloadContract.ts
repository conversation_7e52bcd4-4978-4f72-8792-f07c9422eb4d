import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';

type UseDownloadContract = {
	downloadContract: Function;
};

export function useDownloadContract(): UseDownloadContract {
	function createNotification(): void {
		notification(RequestStatusEnum.SUCCESS, {
			message: `O download do arquivo iniciará em uma nova aba.`,
			title: 'Download',
		});
	}

	function addHTTPS(url): string {
		const protocol ='http:';
		if(url.indexOf(protocol) > -1){
			const domain = url.split(protocol)[1];
			return `https:${domain}`;
		}
		return url;
	}

	function downloadContract(link): void {
		createNotification();
		setTimeout(() => {
			const url = addHTTPS(link);
			const elementA = window.document.createElement('a');
			elementA.href = url;
			elementA.target = "_blank";
			elementA.click();
		}, 1000);
	}

	return {
		downloadContract,
	};
}
