<template>
	<farm-row extra-decrease>
		<farm-box>
			<v-data-table
				hide-default-footer
				:class="{
					'elevation-0 mt-0 table-body-step-two-table': true,
					'mb-8': data.length === 0,
				}"
				id="table-body-step-two"
				ref="tableRef"
				v-model="tableState.selected"
				item-value="id"
				item-selectable="selectable"
				:show-select="true"
				:headers="tableState.headers"
				:items="data"
				:server-items-length="data.length"
				:hide-default-header="events.showCustomHeader()"
			>
				<template slot="no-data">
					<farm-emptywrapper subtitle="Tente filtrar novamente sua pesquisa" />
				</template>
				<template v-slot:header="{ props }" v-if="events.showCustomHeader()">
					<farm-datatable-header
						item-selectable="selectable"
						v-model="props.everyItem"
						firstSelected
						:showCheckbox="true"
						:headers="props.headers"
						:headerProps="props"
						:sortClick="tableState.sortClicked"
						:selectedIndex="5"
						@onClickSort="events.onSort"
						@toggleSelectAll="toggleSelectAll"
					/>
				</template>

				<template v-slot:[`no-data`]>
					<farm-emptywrapper subtitle="Tente adicionar novos recebíveis" />
					<farm-btn @click="onClickButtonImport">
						<farm-icon>upload</farm-icon>
						Upload De Recebíveis
					</farm-btn>
				</template>

				<template v-slot:[`item.data-table-select`]="{ item, isSelected, select }">
					<v-simple-checkbox
						:value="isSelected"
						:readonly="item.selectable"
						:disabled="item.selectable"
						@input="select($event)"
					/>
				</template>
				<template v-slot:[`item.netValue`]="{ item }">
					{{ events.formatMoney(item.netValue) }}
				</template>
				<template v-slot:[`item.expirationDate`]="{ item }">
					{{ events.defaultDateFormat(item.expirationDate) }}
				</template>

				<template v-slot:[`item.infos`]="{ item }">
					<farm-btn
						icon
						title="Ver detalhes"
						color="grey"
						@click="onOpenModalDetails(item)"
					>
						<farm-icon size="md" color="grey">open-in-new</farm-icon>
					</farm-btn>
				</template>

				<template v-slot:footer>
					<farm-datatable-paginator
						v-if="paginationTotalPages > 1"
						class="mt-6 mb-n6"
						:page="paginationPageActive"
						:totalPages="paginationTotalPages"
						@onChangePage="events.onChangePageTable"
						@onChangeLimitPerPage="events.onChangeLimitPerPageTable"
					/>
				</template>
			</v-data-table>
		</farm-box>
	</farm-row>
</template>

<script lang="ts">
import { defineComponent, getCurrentInstance, onMounted } from 'vue';

import { useBody } from '../BodyStepTwoReceivable/composables/useBody';
import { useRedirect } from '../BodyStepTwoReceivable/composables/useRedirect';

import { useTable } from './useTable';

export default defineComponent({
	name: 'table-receivables-operation-cession',
	props: {
		data: {
			type: Array,
			required: true,
		},
		metaData: {
			type: Array,
		},
		paginationTotalPages: {
			type: Number,
		},
		paginationPageActive: {
			type: Number,
		},
	},
	setup(props, { emit }) {
		const internalInstance: any = getCurrentInstance();
		const { events, tableState } = useTable({ emit });
		const { state, operationId } = useBody();

		const { onClickButtonImport } = useRedirect();

		onMounted(() => {
			renderSelected();
		});

		function renderSelected() {
			let dataSelected = [];
			if (operationId.value && props.metaData.length) {
				dataSelected = props.metaData?.map(
					({ id, value, totalLiquidValue, totalFreeValue }) => ({
						id,
						value: totalLiquidValue || value,
						freeValue: totalFreeValue,
					})
				);
			} else {
				dataSelected = state.metaData?.map(item => {
					return {
						id: item.id,
						value: item?.netValue,
						freeValue: item?.freeValue,
					};
				});
			}

			setTimeout(() => {
				for (let i = 0; i < props.data.length; i++) {
					const foundItem = dataSelected.find(item => item?.id === props.data[i]?.id);
					const item = foundItem;
					internalInstance.proxy.$refs.tableRef.select(item);
				}
			}, 1);

			onChangeCheckbox(dataSelected);
		}

		function toggleSelectAll(value: Array<{ id: number }>): void {
			if (value) {
				setTimeout(() => {
					for (let i = 0; i < props.data.length; i++) {
						const item = props.data[i];
						internalInstance.proxy.$refs.tableRef.select(item);
					}
				}, 1);
			} else {
				events.onTableClearSelected();
			}
		}

		function onOpenModalDetails(item) {
			emit('onOpenModalDetails', item);
		}

		function onChangeCheckbox(data): void {
			tableState.selected = data;
		}

		return {
			tableState,
			events,
			toggleSelectAll,
			onChangeCheckbox,
			onOpenModalDetails,
			onClickButtonImport,
		};
	},
});
</script>
<style lang="scss">
@import '@farm-investimentos/front-mfe-components/src/scss/Sticky-table';
@include stickytable('.table-body-step-two-table', 1, (0));
</style>
