import { getCurrentInstance, computed, reactive } from 'vue';

import { brl, defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import { useSelectedProducts } from '@/features/operationCession/composables/useSelectedProducts';
import { headers } from '@/features/operationCession/configurations/headers';

type UseTable = {
	events: {
		showCustomHeader: Function;
		onTableClearSelected: Function;
		defaultDateFormat: Function;
		onChangeLimitPerPageTable: Function;
		onChangePageTable: Function;
		onSort: Function;
		formatMoney: Function;
	};
	tableState: {
		selected: [];
		total: number;
		showModal: boolean;
		totalValues: number;
		sortClicked: [];
		headers: typeof headers;
		tableData: unknown;
		totalNominal: number;
	};
};

type IProps = {
	emit: any;
};

const tableState = reactive({
	total: 0,
	selected: [],
	showModal: false,
	totalValues: 0,
	totalNominal: 0,
	sortClicked: [],
	headers: headers,
	tableData: [],
});

export function useTable({ emit }: IProps): UseTable {
	const { addSelectedDataToCache } = useSelectedProducts();
	const internalInstance: any = getCurrentInstance();
	const breakpoint = computed(() => internalInstance.proxy.$vuetify.breakpoint.name);

	function formatMoney(value = 0) {
		return brl(value);
	}

	function showCustomHeader(): boolean {
		return breakpoint !== 'xs';
	}

	function onSort(data) {
		if (data.field === 'netValue') {
			data.field = 'value';
		}
		const requestData = {
			order: data.descending,
			orderby: data.field,
		};
		emit('onRequest', requestData);
	}

	function onChangePageTable(page: number): void {
		emit('onRequest', {
			page: page - 1,
		});
	}

	function onChangeLimitPerPageTable(limit: number): void {
		emit('onRequest', { page: 0, limit });
	}

	function onTableClearSelected() {
		tableState.total = 0;
		tableState.selected = [];
		addSelectedDataToCache([]);
	}

	tableState.totalValues = computed(() =>
		tableState.selected.reduce(
			(prev, curr) => (prev += Number(curr?.value || curr?.netValue || 0)),
			0
		)
	);

	tableState.totalNominal = computed(() =>
		tableState.selected.reduce((prev, curr) => (prev += Number(curr?.nominalValue || 0)), 0)
	);

	tableState.total = computed(() => tableState.selected?.length);

	return {
		events: {
			showCustomHeader,
			onTableClearSelected,
			defaultDateFormat,
			onChangeLimitPerPageTable,
			onChangePageTable,
			onSort,
			formatMoney,
		},
		tableState,
	};
}
