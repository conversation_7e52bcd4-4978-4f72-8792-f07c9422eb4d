<template>
	<farm-row extra-decrease>
		<farm-box>
			<v-data-table
				hide-default-footer
				:class="{
					'elevation-0 mt-0 table-body-step-two-table': true,
					'mb-8': data.length === 0,
				}"
				id="table-body-step-two-table"
				ref="table"
				checkbox-color="primary"
				v-model="selectedRows"
				item-value="id"
				item-selectable="selectable"
				:show-select="true"
				:headers="headers"
				:items="data"
				:server-items-length="data.length"
				:hide-default-header="showCustomHeader()"
			>
				<template v-slot:[`no-data`]>
					<div class="content-empty-data">
						<farm-emptywrapper
							subtitle="Tente adicionar novos recebíveis"
							:bordered="false"
						/>
						<farm-btn @click="onClickButtonImport">
							<farm-icon>upload</farm-icon>
							Upload De Recebíveis
						</farm-btn>
					</div>
				</template>
				<template v-slot:header="{ props }" v-if="showCustomHeader()">
					<farm-datatable-header
						item-selectable="selectable"
						v-model="all"
						firstSelected
						:showCheckbox="true"
						:headers="props.headers"
						:headerProps="props"
						:sortClick="sortClicked"
						:selectedIndex="5"
						@onClickSort="onSort"
						@toggleSelectAll="toggleSelectAll"
					/>
				</template>
				<template v-slot:[`item.data-table-select`]="{ item, isSelected, select }">
					<v-simple-checkbox
						:value="isSelected"
						:readonly="item.selectable"
						:disabled="item.selectable"
						@input="onClickCheckBox($event, select, item)"
					></v-simple-checkbox>
				</template>
				<template v-slot:[`item.emissionDate`]="{ item }">
					{{ defaultDateFormat(item.emissionDate) }}
				</template>
				<template v-slot:[`item.netValue`]="{ item }">
					<div v-if="item.netValue">{{ formatMoney(item.netValue) }}</div>
					<farm-caption v-else color="gray">Sem precificação</farm-caption>
				</template>
				<template v-slot:[`item.expirationDate`]="{ item }">
					{{ defaultDateFormat(item.expirationDate) }}
				</template>
				<template v-slot:[`item.infos`]="{ item }">
					<farm-btn
						icon
						title="Ver detalhes"
						color="grey"
						@click="onOpenModalDetails(item)"
					>
						<farm-icon size="md" color="grey">open-in-new</farm-icon>
					</farm-btn>
				</template>
				<template v-slot:footer>
					<farm-datatable-paginator
						v-if="data.length > 0"
						class="mt-6 mb-n6"
						:page="paginationPageActive"
						:totalPages="paginationTotalPages"
						@onChangePage="onChangePageTable"
						@onChangeLimitPerPage="onChangeLimitPerPageTable"
					/>
				</template>
			</v-data-table>
		</farm-box>
	</farm-row>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import { brl, defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import { headers } from '../../configurations/headers';

export default defineComponent({
	name: 'table-receivables-V2',
	props: {
		data: {
			type: Array,
			require: true,
		},
		dataSelected: {
			type: Array,
			require: true,
		},
		paginationTotalPages: {
			type: Number,
			require: true,
		},
		paginationPageActive: {
			type: Number,
			default: 1,
		},
		isCleanCheckbox: {
			type: Boolean,
			default: false,
		},
		meta: {
			type: Array,
			require: true,
		},
		forceRender: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			sortClicked: [],
			headers,
			selectedRows: [],
			all: false,
			renderFirst: true,
			cleanItems: false,
			clickItem: false,
		};
	},
	computed: {
		breakpoint(): string {
			return this.$vuetify.breakpoint.name;
		},
	},
	methods: {
		defaultDateFormat,
		formatMoney(value = 0) {
			return brl(value);
		},
		onSort(data): void {
			const requestData = {
				order: data.descending,
				orderby: data.field,
			};
			this.$emit('onRequest', requestData);
		},
		showCustomHeader(): boolean {
			return this.breakpoint !== 'xs';
		},
		onChangePageTable(page: number): void {
			const pageActive = page === 1 ? 0 : page - 1;
			this.$emit('onRequest', { page: pageActive });
		},
		onChangeLimitPerPageTable(limit: number): void {
			this.$emit('onRequest', { page: 0, limit: limit });
		},
		toggleSelectAll(value: Array<{ statusId: number }>): void {
			if (value) {
				setTimeout(() => {
					for (let i = 0; i < this.data.length; i++) {
						const item = this.data[i];
						if (!item.selectable) {
							this.$refs.table.select(item);
						}
					}
				}, 1);
			} else {
				this.cleanItems = true;
				this.renderFirst = false;
				this.forceRender = false;
				this.selectedRows = [];
			}
		},
		onClickButtonImport(): void {
			this.$emit('onClickButtonImport');
		},
		onOpenModalDetails(item): void {
			this.$emit('onOpenModalDetails', item);
		},
		onClickCheckBox(event, onSelect): void {
			this.renderFirst = false;
			this.clickItem = true;
			onSelect(event);
			if(!this.renderFirst ){
				setTimeout(() => {
					this.clickItem = false;
				}, 500);
			}
		},
	},
	watch: {
		data(): void {
			if (this.renderFirst) {
				this.all = null;
				setTimeout(() => {
					for (let i = 0; i < this.data.length; i++) {
						if (this.data[i].selected) {
							const item = this.data[i];
							this.$refs.table.select(item);
						}
					}
					this.selectedRows = [...this.meta];
					this.all = this.selectedRows.length > 0 ? true : null;
				}, 1);
			}
		},
		selectedRows(newValue: Array<{ id: number }>): void {
			this.$emit('onChangeCheckbox', {
				items: newValue,
			});
			if(!this.renderFirst && !this.clickItem ){
				this.clickItem = true;
				setTimeout(() => {
					this.clickItem = false;
				}, 500);
			}
		},
		isCleanCheckbox(newValue: boolean): void {
			if (newValue) {
				this.all = null;
				this.cleanItems = false;
				this.renderFirst = false;
				this.selectedRows = [];
			}
		},
		forceRender(newValue) {
			if (newValue) {
				this.renderFirst = true;
			}
		},
	},
});
</script>
<style lang="scss">
@import '@farm-investimentos/front-mfe-components/src/scss/Sticky-table';
@include stickytable('.table-body-step-two-table', 1, (0));
</style>
<style lang="scss" scoped>
@import './TableReceivablesV2';
</style>
