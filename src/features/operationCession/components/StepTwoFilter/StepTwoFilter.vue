<template>
	<farm-box>
		<farm-form class="mb-6">
			<farm-row>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-emissionDate">
						Data de Emissão (Início/Fim)
					</farm-label>
					<farm-input-rangedatepicker
						ref="datepickerEmissionDate"
						inputId="form-filter-emissionDate"
						v-model="state.emissionDateRange"
						@input="onInputRangedatepickerEmissionDate"
					/>
				</farm-col>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-expiredAtRange">
						Data de Vencimento (Início/Fim)
					</farm-label>
					<farm-input-rangedatepicker
						ref="datepickerExpirationDate"
						inputId="form-filter-expiredAtRange"
						v-model="state.expiredAtRange"
						@input="onInputRangedatepickerExpirationDate"
					/>
				</farm-col>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-value"> <PERSON>or Líquido </farm-label>
					<farm-select
						id="form-filter-value"
						v-model="state.filters.value"
						:items="state.items"
						item-text="label"
						item-value="id"
					/>
				</farm-col>
			</farm-row>
			<farm-row>
				<farm-col>
					<farm-btn-confirm
						outlined
						class="farm-btn--responsive"
						title="Aplicar Filtros"
						@click="onFilterConfirm"
					>
						Aplicar Filtros
					</farm-btn-confirm>
					<farm-btn
						plain
						depressed
						class="farm-btn--responsive ml-0 ml-sm-2 mt-2 mt-sm-0"
						title="Limpar Filtros"
						@click="onFilterClear"
					>
						Limpar Filtros
					</farm-btn>
				</farm-col>
			</farm-row>
		</farm-form>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import { useFilter } from './useFilter';

export default defineComponent({
	name: 'step-two-filter',
	setup(_, { emit }) {
		const {
			state,
			onFilterClear,
			onFilterConfirm,
			onInputRangedatepickerExpirationDate,
			onInputRangedatepickerEmissionDate,
		} = useFilter(emit);
		return {
			state,
			onFilterClear,
			onFilterConfirm,
			onInputRangedatepickerExpirationDate,
			onInputRangedatepickerEmissionDate,
		};
	},
});
</script>
