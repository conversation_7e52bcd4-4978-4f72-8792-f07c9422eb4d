import { computed } from 'vue';

import { useGetter, useStore } from '@/composible';

export function useSelectedProducts() {
	const store = useStore();

	const selectedProductsInCache = computed(useGetter('operationCession', 'selectedInvoices'));

	function addSelectedDataToCache(data): void {
		store.dispatch('operationCession/addOperationSelectedInvoicesCache', { data });
	}

	return {
		selectedProductsInCache,
		addSelectedDataToCache,
	};
}
