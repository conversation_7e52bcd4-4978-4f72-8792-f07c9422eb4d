import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';

import { useSelectedProductId } from '@/composible';
import { exportExcel as exportExcelService } from '@/features/operationCession/services';

type UseExportExcel = {
	onDownloadExcel: Function;
};

export function useExportExcel(): UseExportExcel {
	const productId = useSelectedProductId().value;

	const { mutate } = useMutation({
		mutationFn: params => exportExcelService(params),
	});

	function createNotification(): void {
		notification(RequestStatusEnum.SUCCESS, {
			message: `O download do arquivo iniciará em uma nova aba.`,
			title: 'Download',
		});
	}

	function onDownloadExcel({ id, status }): void {
		createNotification();
		setTimeout(() => {
			mutate({ id, productId, status });
		}, 1000);
	}

	return {
		onDownloadExcel,
	};
}
