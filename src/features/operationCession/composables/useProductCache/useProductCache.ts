import { computed } from 'vue';

import useGetter from '@/composible/useGetter';
import useStore from '@/composible/useStore';

type OperationCessionProductCache = {
	value: {
		idProduct: number;
		nameProduct: string;
		idOperation: number;
		nameOperation: string;
		availableLimit: number;
		dateDisbursement: string;
		valueSeleted: number;
		limit?: number;
		typeOperation?: number;
		productName?: string;
		minValue?: number;
		maxValue?: number;
	};
};

type UseProductCache = {
	operationCessionProductCache: OperationCessionProductCache;
	addDataCache: Function;
};

export function useProductCache(): UseProductCache {
	const store = useStore();

	const operationCessionProductCache = computed(
		useGetter('operationCession', 'operationCessionProductCache')
	);

	function addDataCache(data): void {
		store.dispatch('operationCession/addOperationCessionProductCache', { data });
	}

	return {
		operationCessionProductCache,
		addDataCache,
	};
}
