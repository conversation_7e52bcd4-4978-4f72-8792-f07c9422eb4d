import { computed } from 'vue';

import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';

import { useSelectedProductId } from '@/composible';
import { changeStep as changeStepService } from '@/features/operationCession/services';

type UseChangeStep = {
	isLoadingStepPatch: {
		value: boolean
	};
	isErrorStepPatch: {
		value: boolean
	};
	changeStep: Function;
};

export function useChangeStep(): UseChangeStep {
	let callFunc: Function | null = null;
	const productId = useSelectedProductId().value;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: ({step, id, productId}) =>{
			return changeStepService({step, id, productId});
		},
		onSuccess: () => {
			if(callFunc) callFunc(false);
		},
		onError: () => {
			createNotificationError();
			if(callFunc) callFunc(true);
		},
	});

	function createNotificationError(): void {
		notification(RequestStatusEnum.ERROR, 'Error ao tentar mudar de página. Por favor tente novamente!');
	}

	const isLoadingStepPatch = computed(() => {
		return isLoading.value;
	});

	const isErrorStepPatch = computed(() => {
		return isError.value;
	});

	function changeStep({newStep, id}, callback): void {
		mutate({ step: newStep, id, productId });
		callFunc = callback;
	}

	return {
		isLoadingStepPatch,
		isErrorStepPatch,
		changeStep,
	};
}
