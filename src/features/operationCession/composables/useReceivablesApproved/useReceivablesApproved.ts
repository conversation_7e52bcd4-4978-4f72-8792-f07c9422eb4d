import { computed, ref } from 'vue';

import { queryString } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';

import { useSelectedProductId } from '@/composible';
import { builderReceivablesApproved } from '@/features/operationCession/helpers/builderReceivablesApproved';
import { getReceivablesApproved as getReceivablesApprovedService } from '@/features/operationCession/services';
import { DenyReasons } from '@/features/operationCession/types';

type UseReceivablesApproved = {
	receivablesApproved: Array<DenyReasons>;
	receivablesApprovedPagination: any;
	isLoadingReceivablesApproved: {
		value: boolean
	};
	isErrorReceivablesApproved: {
		value: boolean
	};
	getReceivablesApproved: Function;
};

export function useReceivablesApproved(): UseReceivablesApproved {
	const receivablesApproved = ref([]);
	const receivablesApprovedPagination = ref({});

	const productId = useSelectedProductId().value;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: params => getReceivablesApprovedService(params),
		onSuccess: data => {


			const { content, pagination } = builderReceivablesApproved(data.data);
			receivablesApproved.value = content;
			receivablesApprovedPagination.value = pagination;
		},
	});

	const isLoadingReceivablesApproved = computed(() => {
		return isLoading.value;
	});

	const isErrorReceivablesApproved = computed(() => {
		return isError.value;
	});

	function getReceivablesApproved(id, filters): void {
		const params = queryString(filters, {});
		mutate({ productId, id, filters: params });
	}

	return {
		receivablesApprovedPagination,
		receivablesApproved,
		isLoadingReceivablesApproved,
		isErrorReceivablesApproved,
		getReceivablesApproved,
	};
}
