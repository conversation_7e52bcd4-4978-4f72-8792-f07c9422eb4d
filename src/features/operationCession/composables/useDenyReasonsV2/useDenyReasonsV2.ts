import { computed, ref } from 'vue';

import { queryString } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';

import { useSelectedProductId } from '@/composible';
import { builderDenyReasonsV2 } from '@/features/operationCession/helpers/builderDenyReasonsV2';
import { getDenyReasonsV2 as getDenyReasonsV2Service } from '@/features/operationCession/services';

type UseDenyReasonsV2 = {
	denyReasonV2: Array<any>;
	denyReasonPagination: any;
	isLoadingDenyReason: {
		value: boolean
	};
	isErrorDenyReason: {
		value: boolean
	};
	getDenyReasonV2: Function;
};

export function useDenyReasonsV2(): UseDenyReasonsV2 {
	const denyReasonV2 = ref([]);
	const denyReasonPagination = ref({});

	const productId = useSelectedProductId().value;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: params => getDenyReasonsV2Service(params),
		onSuccess: response => {
			const { content, pagination } = builderDenyReasonsV2(response.data);
			denyReasonV2.value = content;
			denyReasonPagination.value = pagination;
		},
	});

	const isLoadingDenyReason = computed(() => {
		return isLoading.value;
	});

	const isErrorDenyReason = computed(() => {
		return isError.value;
	});

	function getDenyReasonV2(id, filters): void {
		const params = queryString(filters, {});
		mutate({ productId, id, filters: params });
	}

	return {
		denyReasonV2,
		denyReasonPagination,
		isLoadingDenyReason,
		isErrorDenyReason,
		getDenyReasonV2,
	};
}
