import { computed } from 'vue';

import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from "@tanstack/vue-query";

import { useSelectedProductId } from '@/composible';
import {
	updateStatusCancelCession as updateStatusCancelCessionService
} from '@/features/operationCession/services';

type UseCancelCession = {
	isLoadingCancel: {
		value: boolean
	};
	cancelCession: Function;
}

export function useCancelCession(): UseCancelCession {

	let callFunc: Function | null = null;
	const productId = useSelectedProductId().value;

	const { isLoading, mutate } = useMutation({
		mutationFn: ({ id }) => {
			return updateStatusCancelCessionService({
				id,
				productId
			});
		},
		onSuccess: () => {
			createNotification();
			if(callFunc !== null){
				setTimeout(() => {
					callFunc();
				}, 1500);
			}
		},
	});

	const isLoadingCancel = computed(() => {
		return isLoading.value;
	});

	function createNotification(): void {
		notification(RequestStatusEnum.SUCCESS, 'Cessão cancelada com sucesso!');
	}

	function cancelCession(id: number, callback: Function): void {
		mutate({ id });
		callFunc = callback;
	}

	return {
		isLoadingCancel,
		cancelCession
	};
}
