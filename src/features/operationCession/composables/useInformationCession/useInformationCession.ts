import { ref, computed } from 'vue';

import { useMutation } from '@tanstack/vue-query';

import { useSelectedProductId } from '@/composible';
import { builderInformationCession } from '@/features/operationCession/helpers/builderInformationCession';
import { getInformationCession as getInformationCessionService } from '@/features/operationCession/services';

type UseInformationCession = {
	informationCessions: {
		value: any;
	};
	isLoadingInformationCession: {
		value: boolean;
	};
	isErrorInformationCession: {
		value: boolean;
	};
	getInformationCession: Function;
};

export function useInformationCession(): UseInformationCession {
	const informationCessions = ref([]);
	let callFunc: Function | null = null;
	const productId = useSelectedProductId().value;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: ({ operationId, productId }) => {
			return getInformationCessionService({ operationId, productId });
		},
		onSuccess: response => {
			informationCessions.value = builderInformationCession(response.data);
			if (callFunc) callFunc(informationCessions.value);
		},
		onError: () => {
			informationCessions.value = {};
		},
	});

	const isLoadingInformationCession = computed(() => {
		return isLoading.value;
	});

	const isErrorInformationCession = computed(() => {
		return isError.value;
	});

	function getInformationCession(id: number, callback: Function): void {
		mutate({ operationId: id, productId });
		callFunc = callback;
	}

	return {
		informationCessions,
		isLoadingInformationCession,
		isErrorInformationCession,
		getInformationCession,
	};
}
