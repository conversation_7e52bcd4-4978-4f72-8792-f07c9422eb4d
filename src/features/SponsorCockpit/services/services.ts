import { environment, file as downloadFileHandler } from '@farm-investimentos/front-mfe-libs-ts';

import { client as financingClient } from '@/configurations/services/financing';
import type {
	SponsorCockpitListResponse,
	SponsorCockpitSummaryResponse,
} from '@/features/SponsorCockpit/types/types';

export const getSponsorCockpitList = (params: string, key_account_id: number) => {
	return financingClient.get<SponsorCockpitListResponse>(
		`/cockpit/${key_account_id}/list?${params}`
	);
};

export const getSponsorCockpitListReprocess = (params: string, key_account_id: number) => {
	return financingClient.get<SponsorCockpitListResponse>(
		`/cockpit/${key_account_id}/workflow/error?${params}`
	);
};

export const getSponsorCockpitListReprocessIds = (params: string, key_account_id: number) => {
	return financingClient.get<SponsorCockpitListResponse>(
		`/cockpit/${key_account_id}/workflow/error_ids?${params}`
	);
};

export const getSponsorCockpitSummary = (params: string, key_account_id: number) => {
	return financingClient.get<SponsorCockpitSummaryResponse>(
		`/cockpit/${key_account_id}/summary?${params}`
	);
};

export const getSources = () => financingClient.get(`cockpit/source`);

export const getSponsorCockpitStatus = () => financingClient.get(`cockpit/status`);

export const getErrors = () => financingClient.get(`cockpit/workflow/error_status`);

export const getSponsorCockpitDetails = (keyAccountId: number, operationId: number) => {
	return financingClient.get(`/cockpit/${keyAccountId}/${operationId}`);
};

export const getSponsorCockpitLog = (workingCapitalId: number) => {
	return financingClient.get(`/workingCapital/${workingCapitalId}/workflow/logs`);
};

export const getSponsorCockpitExport = ({ key_account_id, operation_type, params }) => {
	const domain = environment.apiExcelUrl;
	const url = `/v1/products/${key_account_id}/download/excel/cockpit?operation_type=${operation_type}&${params}`;
	downloadFileHandler(`${domain}${url}`);
};

export const sendReprocessRequest = (params: string) => {
	return financingClient.post(`/cockpit/workflow/retry`, params);
};
