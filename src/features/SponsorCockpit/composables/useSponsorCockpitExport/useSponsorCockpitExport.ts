import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';
import { queryString } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';

import { useSelectedProductId } from '@/composible';
import {getSponsorCockpitExport as getSponsorCockpitExportService,
} from '@/features/SponsorCockpit/services';

type UseSponsorCockpitExport = {
	exportSponsorCockpit: Function;
};

export function useSponsorCockpitExport(): UseSponsorCockpitExport {

	const key_account_id = useSelectedProductId().value;

	const { mutate } = useMutation({
		mutationFn: (params) => getSponsorCockpitExportService(params),
	});

	function createNotification(): void {
		notification(RequestStatusEnum.SUCCESS, {
			message: `O download do arquivo iniciará em uma nova aba.`,
			title: 'Download',
		});
	}

	function exportSponsorCockpit(operation_type: number, filters: any): void {
		createNotification();
		const params = queryString(filters, {});

		setTimeout(() => {
			mutate({ key_account_id , operation_type, params });
		}, 1000);
	}

	return {
		exportSponsorCockpit
	};
}
