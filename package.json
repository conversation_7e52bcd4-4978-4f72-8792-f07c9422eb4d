{"name": "app-wallet", "version": "2.8.44", "private": true, "scripts": {"local": "concurrently \"vue-cli-service serve --mode local\" \"npm run mock\"", "serve": "vue-cli-service serve", "serve:local": "vue-cli-service serve --mode local", "mock": "node ./mocks/server.js", "build": "vue-cli-service build --mode production", "test:unit": "jest --runInBand", "test:coverage": "jest --coverage --runInBand", "lint": "vue-cli-service lint", "postinstall": "husky install", "storybook": "start-storybook -p 6006", "build-storybook": "build-storybook"}, "dependencies": {"@farm-investimentos/front-mfe-components": "^15.12.1", "@farm-investimentos/front-mfe-libs-ts": "^3.0.0", "@tanstack/vue-query": "4.29.20", "@vue/composition-api": "^1.7.2", "apexcharts": "^3.26.0", "axios": "^0.21.1", "core-js": "^3.6.5", "dotenv": "^8.2.0", "number-abbreviate": "^2.0.0", "single-spa-vue": "^1.5.2", "systemjs-webpack-interop": "^1.1.0", "text-mask-addons": "^3.8.0", "tslib": "^2.8.1", "uuid": "9.0.1", "vue": "2.7.10", "vue-apexcharts": "^1.6.0", "vue-carousel-card": "^1.0.7", "vue-lottie": "^0.2.1", "vue-router": "^3.5.4", "vue-upload-component": "^2.8.22", "vue2-transitions": "^0.3.0", "vuejs-dialog": "^1.4.2", "vuetify": "^2.4.5", "vuetify-loader": "^1.7.0", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "7.17.9", "@babel/generator": "7.17.9", "@commitlint/cli": "~17.0.0", "@commitlint/config-conventional": "~17.0.0", "@storybook/addon-actions": "^6.5.9", "@storybook/addon-essentials": "^6.5.9", "@storybook/addon-interactions": "^6.5.9", "@storybook/addon-links": "^6.5.9", "@storybook/builder-webpack4": "^6.5.9", "@storybook/manager-webpack4": "^6.5.9", "@storybook/testing-library": "^0.0.13", "@storybook/vue": "^6.5.9", "@types/jest": "^24.9.1", "@typescript-eslint/eslint-plugin": "^4.18.0", "@typescript-eslint/parser": "^4.18.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-plugin-unit-jest": "^4.5.12", "@vue/cli-service": "^4.5.13", "@vue/eslint-config-typescript": "^7.0.0", "@vue/test-utils": "^1.3.6", "babel-eslint": "^10.1.0", "babel-jest": "^26.6.3", "babel-loader": "^8.2.5", "concurrently": "^6.2.0", "error-stack-parser": "2.0.7", "eslint": "^6.7.2", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.3.1", "eslint-plugin-standard": "^5.0.0", "eslint-plugin-vue": "^6.2.2", "husky": "^7.0.0", "jest": "^26.6.3", "json-server": "^0.17.1", "npm": "^8.12.1", "sass": "~1.32.0", "sass-loader": "^10.0.0", "ts-jest": "^26.5.6", "typescript": "~4.1.5", "vue-cli-plugin-single-spa": "^1.1.0", "vue-cli-plugin-vuetify": "~2.2.0", "vue-jest": "^3.0.7", "vue-template-compiler": "2.7.10"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "volta": {"node": "14.21.3"}}